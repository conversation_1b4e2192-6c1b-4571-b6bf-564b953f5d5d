[build-system]
requires = [
    "hatchling>=1.24.2",
]
build-backend = "hatchling.build"

[project]
name = "NI-Compute"
description="NI Compute Subnet"
author="neuralinternet.ai"
authors = [
     {name = "Neural Internet"},
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Topic :: Software Development :: Build Tools",
    "License :: OSI Approved :: MIT License",
    "Natural Language :: English",
    "Programming Language :: Python :: 3 :: Only",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: Implementation :: CPython",
    "Environment :: GPU :: NVIDIA CUDA :: 12",
    "Operating System :: POSIX :: Linux",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Mathematics",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development",
    "Topic :: Software Development :: Libraries",
    "Topic :: Software Development :: Libraries :: Python Modules",
]
dynamic = ["version"]
dependencies = [
    #"bittensor[cli]",
    # this is a copy of previous reqs.txt
    # TODO: versions shouldn't usually be pinned this way here going forward
    # (pip-tools maintain version pins automatically and this will complicate upgrades)
    "at==0.0.3",
    "bittensor==9.0.0",
    "cryptography==43.0.1",
    "cryptography",
    "docker==7.0.0",
    "GPUtil==1.4.0",
    "igpu==0.1.2",
    "numpy==2.0.2",
    "psutil==5.9.8",
    "pyinstaller==6.4.0",
    "wandb==0.19.0",
    "pyfiglet==1.0.2",
    "python-dotenv==1.0.1",
    "requests==2.31.0",
    "paramiko==3.4.1",
    "blake3",
    "ipwhois==1.3.0",
    "torch==2.5.1",
    "google-auth==2.40.0",
    "google-cloud-core==2.4.3",
    "google-cloud-pubsub==2.29.0",
]

[project.optional-dependencies]
dev = [
    "bittensor[cli]",
    "pip-tools",
    "pre-commit",
    "pytest",
    "pytest-cov",
    "allure-pytest"
]

[project.urls]
Homepage = "https://neuralinternet.ai/"
Sponsor = "https://bittensor.org"
History = "https://github.com/neuralinternet/ni-compute/releases"
Tracker = "https://github.com/neuralinternet/ni-compute/issues"
Source = "https://github.com/neuralinternet/ni-compute"

[tool.hatch.version]
path = "compute/__init__.py"

[tool.hatch.build.targets.sdist]
exclude = [
    "/.github",
]

[tool.hatch.build.targets.wheel]
packages = [
    "compute",
    "neurons",
]

[tool.pytest.ini_options]
addopts = "-v --cov=. --cov-report=term-missing"
testpaths = [
    "tests"
]
