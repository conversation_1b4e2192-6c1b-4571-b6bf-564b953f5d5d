repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        stages: [pre-commit, manual]
      - id: end-of-file-fixer
        stages: [pre-commit, manual]
  - repo: https://github.com/alessandrojcm/commitlint-pre-commit-hook
    rev: v9.20.0
    hooks:
      - id: commitlint
        stages: [commit-msg]
        additional_dependencies: ['@commitlint/config-conventional']

  # Local repository for a custom hook
  - repo: local
    hooks:
      - id: check-branch-name
        name: 'Check Branch Name'
        entry: ./scripts/check-current-branch.sh
        stages: [pre-commit, pre-push, manual]
        language: system
        files: /dev/null
        always_run: true
        verbose: true
  - repo: local
    hooks:
      - id: post-checkout-check
        name: 'Check Branch Name'
        entry: ./scripts/check-current-branch.sh
        stages: [post-checkout]
        language: system
        always_run: true
        verbose: true
  - repo: local
    hooks:
      - id: pip-compile
        name: 'Resolve requirements'
        entry: python -m piptools compile --quiet -o requirements.txt
        stages: [pre-commit, manual]
        language: system
        files: pyproject.toml
        verbose: true
  - repo: local
    hooks:
      - id: pip-compile-dev
        name: 'Resolve dev requirements'
        entry: python -m piptools compile --quiet --extra dev -o requirements-dev.txt
        stages: [pre-commit, manual]
        language: system
        files: pyproject.toml
        verbose: true
  - repo: local
    hooks:
      - id: run-pytest
        name: 'Run Pytest'
        entry: python -m pytest --alluredir allure-results
        language: system
        stages: [pre-commit, manual]
        always_run: true
        verbose: true
        pass_filenames: false
