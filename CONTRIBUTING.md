# New Initiative: Reward Program for the Compute Subnet Community

We're excited to announce a new initiative to reward our community members for their valuable contributions to the Compute Subnet! Our goal is to foster innovation, improve the network's functionality, and enhance user experience through community-driven development. If you've been looking to contribute, now is the perfect time to get involved and potentially earn a reward for your efforts.

## Reward Range

We are offering rewards ranging from $600 to $10,000 for pull requests (PRs) that add significant value to the Compute Subnet. The aim is to acknowledge the hard work, creativity, and impact of contributions that help propel the network forward.

## Evaluation Criteria

- **Value Addition**: The PR should introduce features, fixes, or enhancements that significantly benefit the Compute Subnet's functionality, performance, security, or user experience.
- **Quality of Work**: High-quality code, documentation, and adherence to best practices will be key factors in our evaluation.
- **Innovation**: We appreciate innovative solutions that address existing challenges or introduce new opportunities for the network.

## Process and Payment

1. **Submit Your PR**: Contribute to the Compute Subnet by submitting your PR through our GitHub repository. [Submit Here](https://github.com/neuralinternet/compute-subnet)
2. **Review Process**: Our team will thoroughly review each submission to assess its impact, quality, and the value it adds to the network. This process may involve discussions, requests for changes, or further clarifications to ensure the contribution meets our standards and objectives.
3. **Reward Determination**: Once a PR is approved, the reward amount will be determined based on the contribution's overall value and the effort involved. We aim to make this process as transparent and fair as possible.
4. **Legal Compliance**: To comply with tax regulations and ensure a smooth payout process, we require all contributors eligible for a reward to complete a W-8 (for non-U.S. persons) or W-9 (for U.S. persons) form. This step is crucial for us to file the necessary 1099 forms and cover the expenses associated with these filings.
5. **Payment Release**: Upon successful review of the contribution and completion of the required tax documentation, the reward will be processed and released to the contributor.

## Get Started

This initiative is open to developers, technologists, and enthusiasts passionate about decentralized computing and blockchain technology. Whether you're a long-time member of our community or new to the Compute Subnet, we encourage you to dive in, explore open issues, and bring your ideas to life.

Together, we can build a stronger, more efficient, and inclusive Compute Subnet. We're looking forward to seeing your innovative contributions and are excited to reward your efforts in making a significant impact on the network!

For more details about the contribution process, review guidelines, and tax documentation requirements, please visit our [Contribution Guide on GitHub](#contribution-guide-for-the-compute-subnet).

## Let's innovate together and shape the future of decentralized computing!

---

# Contribution Guide for the Compute Subnet

## Getting Started

Before you begin, it's important to familiarize yourself with the project. Here are a few steps to get you started:

1. **Explore the Repository**: Visit the [Compute Subnet GitHub repository](https://github.com/neuralinternet/compute-subnet) to explore the existing codebase, open issues, and ongoing projects.
2. **Join the Community**: Engage with the Compute Subnet community on [Discord](https://discord.gg/t7BMee4w). It's a great way to get help, discuss ideas, and stay updated.
3. **Review Open Issues**: Look at the open issues for areas where you can contribute. Some issues may be tagged with "good first issue" for newcomers.

## Making Contributions

### Submitting a Pull Request (PR)

1. **Fork the Repository**: Start by forking the repository to your GitHub account.
2. **Clone the Fork**: Clone your fork locally to start working on the changes.
   ```sh
   git clone https://github.com/your-username/compute-subnet.git
   ```
3. **Create a New Branch**: For each new feature or fix, create a new branch from `main`.
   ```sh
   git checkout -b feature/your-feature-name
   ```
4. **Make Your Changes**: Implement your changes, ensuring you adhere to the coding standards and best practices.
5. **Write Tests**: If applicable, write tests for your changes to ensure reliability and prevent future regressions.
6. **Document Your Changes**: Update the README.md or relevant documentation to reflect any new features or important changes.
7. **Commit Your Changes**: Use meaningful commit messages that clearly explain your changes.
   ```sh
   git commit -am "Add a concise and clear description of your change"
   ```
8. **Push to Your Fork**: Push your changes to your fork on GitHub.
   ```sh
   git push origin feature/your-feature-name
   ```
9. **Submit a Pull Request**: Go to the original Compute Subnet repository, and you'll see a prompt to submit a pull request. Ensure your PR description clearly describes the changes and their impact.

### PR Review Process

Once submitted, your PR will undergo a review process by the project maintainers. The process includes:

- **Initial Review**: The maintainers will review the PR for its relevance, quality, and alignment with the project goals.
- **Feedback and Changes**: You may receive feedback and requests for changes. Engaging in discussions and making requested changes is part of the process.
- **Approval and Merge**: Once approved, your PR will be merged into the main codebase.

## Rewards Program

For contributions that add significant value, we offer rewards ranging from $600 to $10,000. The reward amount depends on the impact, effort, and quality of the contribution. Please refer to our [Rewards Program]() document for more details, including the evaluation criteria and payout process.

## Legal and Compliance

Contributors eligible for rewards are required to complete a W-8 (for non-U.S. persons) or W-9 (for U.S. persons) form to comply with tax regulations. This step is essential for filing the necessary 1099 forms.

## Code of Conduct

We are committed to providing a welcoming and inclusive environment. All participants are expected to adhere to our [Code of Conduct](). Respect, professionalism, and constructive communication are paramount.

## Questions?

If you have any questions or need further guidance, don't hesitate to ask for help in our [community channels](https://discord.gg/t7BMee4w). Our community and maintainers are here to support you.

Thank you for contributing to the Compute Subnet! Together, we're building the future of decentralized computing.
