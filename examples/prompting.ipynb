{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "This notebook is designed to showcase how to build your own subnet, modeled after the \n", "Bittensor Text Prompting subnet. This subnet is designed to take in a list of messages\n", "and a list of roles, and generate a completion based on the current state of the network.\n", "\n", "To create a subnet, you must define three (3) main elements: \n", "- the protocol message format (synapse subclass)\n", "- the miner (how to complete requests)\n", "- the validator (how to validate requests)\n", "\n", "Below is a simple implementation of the protocol, so you can see how it works.\n", "\n", "You can also use the `deserialize` method to specify how to deserialize the data\n", "into a tensor. This is useful for converting strings into tensors, for example.\n", "\n", "You must also define your priority and blacklist functions. See below:\n", "\"\"\"\n", "\n", "\n", "import bittensor as bt\n", "import pydantic\n", "import time\n", "import torch\n", "bt.trace()\n", "\n", "from typing import List, Dict, Optional, Tuple, Union, Callable\n", "# Subnet creator controls\n", "class Prompting( bt.Synapse ):\n", "    \"\"\"\n", "    Represents the core component of a Synapse for handling and controlling message prompting in a network.\n", "    \n", "    The Prompting synapse captures roles and messages, and can generate a completion based on the current state. \n", "    It also contains unique hashes for roles and messages to ensure integrity and uniqueness.\n", "    \n", "    Attributes:\n", "        roles (List[str]): A list of roles associated with this synapse. Immutable post-instantiation.\n", "        messages (List[str]): A list of messages associated with this synapse. Immutable post-instantiation.\n", "        completion (str): A field to store the completion or result after processing.\n", "        messages_hash (str): An immutable hash representing the messages in the prompting scenario.\n", "        roles_hash (str): An immutable hash representing the roles in the prompting scenario.\n", "    \"\"\"\n", "    class Config: validate_assignment = True\n", "    def deserialize( self ): return self.completion\n", "\n", "    roles: List[str] = pydantic.Field(..., allow_mutation=False)\n", "    messages: List[str] = pydantic.Field(..., allow_mutation=False)\n", "    completion: str = None\n", "\n", "    @property\n", "    def required_hash_fields(self) -> List[str]:\n", "        \"\"\" Returns the list of fields that are essential for hash computation. \"\"\"\n", "        return ['messages']\n", "\n", "def prompt( synapse: Prompting ) -> Prompting:\n", "    \"\"\"\n", "    Process the provided synapse to generate a completion.\n", "\n", "    Args:\n", "        synapse (Prompting): The input synapse to be processed.\n", "\n", "    Returns:\n", "        Prompting: The updated synapse with a completion.\n", "    \"\"\"\n", "    bt.logging.debug(\"In prompt!\")\n", "    synapse.completion = \"I am a chatbot\"\n", "    return synapse\n", "\n", "def blacklist( synapse: Prompting ) -> <PERSON><PERSON>[bool, str]:\n", "    \"\"\"\n", "    Determines if the provided synapse should be blacklisted.\n", "\n", "    Args:\n", "        synapse (Prompting): The input synapse to be evaluated.\n", "\n", "    Returns:\n", "        Tuple[bool, str]: A tuple containing a boolean that indicates whether the synapse is blacklisted,\n", "                          and a string providing the reason.\n", "    \"\"\"\n", "    return False, \"\"\n", "\n", "def priority( synapse: Prompting ) -> float:\n", "    \"\"\"\n", "    Determines the priority of the provided synapse.\n", "\n", "    Args:\n", "        synapse (Prompting): The input synapse to be evaluated.\n", "\n", "    Returns:\n", "        float: The priority value of the synapse, with higher values indicating higher priority.\n", "    \"\"\"\n", "    return 0.0\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:     Started server process [1386675]\n", "INFO:     Waiting for application startup.\n", "TRACE:    ASGI [1] Started scope={'type': 'lifespan', 'asgi': {'version': '3.0', 'spec_version': '2.0'}, 'state': {}}\n", "TRACE:    ASGI [1] Receive {'type': 'lifespan.startup'}\n", "TRACE:    ASGI [1] Send {'type': 'lifespan.startup.complete'}\n", "INFO:     Application startup complete.\n", "INFO:     <PERSON><PERSON><PERSON> running on http://0.0.0.0:8099 (Press CTRL+C to quit)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m2023-10-08 19:50:45.555\u001b[0m | \u001b[34m\u001b[1m     DEBUG      \u001b[0m | dendrite | --> | 3908 B | Prompting | 5C86aJ2uQawR6P6veaJQXNK9HaWh6NMbUhTiLs65kq4ZW3NH | 149.137.225.62:8099 | 0 | Success\n"]}, {"name": "stderr", "output_type": "stream", "text": ["TRACE:    127.0.0.1:45274 - HTTP connection made\n", "TRACE:    127.0.0.1:45274 - ASGI [2] Started scope={'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('127.0.0.1', 8099), 'client': ('127.0.0.1', 45274), 'scheme': 'http', 'method': 'POST', 'root_path': '', 'path': '/Prompting', 'raw_path': b'/Prompting', 'query_string': b'', 'headers': '<...>', 'state': {}}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m2023-10-08 19:50:45.574\u001b[0m | \u001b[34m\u001b[1m     DEBUG      \u001b[0m | axon     | <-- | 867 B | Prompting | 5C86aJ2uQawR6P6veaJQXNK9HaWh6NMbUhTiLs65kq4ZW3NH | 127.0.0.1:45274 | 200 | Success \n"]}, {"name": "stderr", "output_type": "stream", "text": ["TRACE:    127.0.0.1:45274 - ASGI [2] Receive {'type': 'http.request', 'body': '<867 bytes>', 'more_body': False}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m2023-10-08 19:50:45.578\u001b[0m | \u001b[34m\u001b[1m     DEBUG      \u001b[0m | In prompt!                    \n"]}, {"name": "stderr", "output_type": "stream", "text": ["TRACE:    127.0.0.1:45274 - ASGI [2] Send {'type': 'http.response.start', 'status': 200, 'headers': '<...>'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m2023-10-08 19:50:45.585\u001b[0m | \u001b[34m\u001b[1m     DEBUG      \u001b[0m | axon     | --> | 820 B | Prompting | 5C86aJ2uQawR6P6veaJQXNK9HaWh6NMbUhTiLs65kq4ZW3NH | 127.0.0.1:45274  | 200 | Success\n", "INFO:     127.0.0.1:45274 - \"POST /Prompting HTTP/1.1\" 200 OK\n"]}, {"name": "stderr", "output_type": "stream", "text": ["TRACE:    127.0.0.1:45274 - ASGI [2] Send {'type': 'http.response.body', 'body': '<820 bytes>', 'more_body': True}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[34m2023-10-08 19:50:45.589\u001b[0m | \u001b[34m\u001b[1m     DEBUG      \u001b[0m | dendrite | <-- | 4462 B | Prompting | 5C86aJ2uQawR6P6veaJQXNK9HaWh6NMbUhTiLs65kq4ZW3NH | 149.137.225.62:8099 | 200 | Success\n", "\u001b[34m2023-10-08 19:50:45.589\u001b[0m | \u001b[34m\u001b[1m     DEBUG      \u001b[0m | dendrite | <-- | 4462 B | Prompting | 5C86aJ2uQawR6P6veaJQXNK9HaWh6NMbUhTiLs65kq4ZW3NH | 149.137.225.62:8099 | 200 | Success\n"]}, {"name": "stderr", "output_type": "stream", "text": ["TRACE:    127.0.0.1:45274 - ASGI [2] Send {'type': 'http.response.body', 'body': '<0 bytes>', 'more_body': False}\n"]}, {"data": {"text/plain": ["[Prompting(roles=['user'], messages=['hello, who are you?'], completion='I am a chatbot')]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}, {"name": "stderr", "output_type": "stream", "text": ["TRACE:    127.0.0.1:45274 - ASGI [2] Receive {'type': 'http.disconnect'}\n", "TRACE:    127.0.0.1:45274 - ASGI [2] Completed\n"]}, {"name": "stderr", "output_type": "stream", "text": ["TRACE:    127.0.0.1:45274 - HTTP connection lost\n"]}], "source": ["# Create an Axon instance on port 8099.\n", "axon = bt.axon(port=8099)\n", "\n", "# Attach the forward, blacklist, and priority functions to the Axon.\n", "# forward_fn: The function to handle forwarding logic.\n", "# blacklist_fn: The function to determine if a request should be blacklisted.\n", "# priority_fn: The function to determine the priority of the request.\n", "axon.attach(\n", "    forward_fn=prompt,\n", "    blacklist_fn=blacklist,\n", "    priority_fn=priority\n", ")\n", "\n", "# Start the Axon to begin listening for requests.\n", "axon.start()\n", "\n", "# Create a Dendrite instance to handle client-side communication.\n", "d = bt.dendrite()\n", "\n", "# Send a request to the Axon using the Dendrite, passing in a StreamPrompting instance with roles and messages.\n", "# The response is awaited, as the Dendrite communicates asynchronously with the Axon.\n", "resp = await d(\n", "    [axon],\n", "    Prompting(roles=[\"user\"], messages=[\"hello, who are you?\"]),\n", "    deserialize=False\n", ")\n", "\n", "# The response object contains the result of the prompting operation.\n", "resp"]}], "metadata": {"kernelspec": {"display_name": "rev2", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}